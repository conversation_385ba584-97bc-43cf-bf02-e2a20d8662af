<template>
  <div class="mb-4 text-right">
    <search :searchData="searchData" @search="refresh">
      <mw-button
        @click="exportOrder"
        :loading="exportLoading"
        :disabled="!data?.length"
      >
        导出Excel
      </mw-button>
    </search>
  </div>
  <mw-table
    :scroll="{ x: 'max-content' }"
    :columns="columns"
    :data-source="data"
    :loading="loading"
    :rowKey="(record) => record.id"
    hasPage
    @change="onTableChange"
    :pageConfig="paginationProps"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex == 'materialTypeCount'">
        <div class="">{{ record.materialTypeCount || 0 }}种</div>
      </template>

      <template v-if="column.key == 'button'">
        <mw-button
          title="详情"
          @click="onOpenDetail(record.bomNo)"
          v-permission="'production:bomMarketOrder:detail'"
        ></mw-button>

        <!-- <a-button type="primary" @click="onOpen(record.id)" class="ml-2"   :disabled="(record.status!==0 && record.status!==1)?true:false">
          编辑
        </a-button> -->

        <!-- <a-popconfirm
        detailContractReviewDrawer
        title="确定是否删除"
        ok-text="是"
        cancel-text="否"
        @confirm="onConfirm(record)"
        @cancel="onCancel(record)">
        <a-button type="primary" danger class="ml-2">
          删除
        </a-button>
      </a-popconfirm> -->
      </template>
    </template>
  </mw-table>
  <!-- 新增 -->
  <add-contract-review-drawer
    ref="refAddContractReviewDrawer"
    v-model:visible="visibleAddContractReviewDrawer"
    @finish="getList"
    v-model:id="contractReviewId"
  />
  <detail-contract-review-drawer
    ref="refVisibleDetail"
    v-model:visible="visibleDetail"
    v-model:id="contractReviewId"
  />
</template>
<script setup>
import Search from "@/components/search/index.vue";
import { ref, onBeforeMount, reactive, toRaw, getCurrentInstance } from "vue";
import { usePagenation } from "@/common/setup";
import addContractReviewDrawer from "./addContractReviewDrawer.vue";
import detailContractReviewDrawer from "./detailContractReviewDrawer.vue";
import {
  getBomMarketOrderPage,
  exportOrderBom,
} from "@/api/basicData/orderBomChange.js";
import { useRouter } from "vue-router";
import {
  orderBOMType,
  wholeType,
  orderTypeList,
  orderBomIsLockedList,
  productMarketClassificationList,
} from "@/common/constant.js";
import { exportExecl } from "@/utils/util.js";

const router = useRouter();

const columns = ref([
  {
    title: "BOM名称",
    dataIndex: "bomName",
    key: "bomName",
  },
  {
    title: "关联订单编号",
    dataIndex: "marketOrderNo",
    key: "marketOrderNo",
  },
  {
    title: "锁定状态",
    dataIndex: "isLocked",
    key: "isLocked",
    customRender: ({ record }) => {
      if (record.isLocked == 0) {
        return "未锁定";
      } else if (record.isLocked == 1) {
        return record.lockTime
          ? "已锁定" + " ( " + record.lockTime + " ) "
          : "已锁定";
      }
    },
  },
  {
    title: "状态",
    dataIndex: "statusType",
    key: "statusType",
  },
  {
    title: "物料种类",
    dataIndex: "materialTypeCount",
    key: "materialTypeCount",
  },
  {
    title: "业务所属人",
    dataIndex: "bizBelongUserName",
    key: "bizBelongUserName",
  },
  {
    title: "客户代码",
    dataIndex: "customerNo",
    key: "customerNo",
  },

  {
    title: "创建时间",
    dataIndex: "createTime",
    key: "createTime",
  },
  {
    title: "备注",
    dataIndex: "remark",
    key: "remark",
  },
  {
    title: "操作",
    dataIndex: "button",
    key: "button",
    fixed: "right",
  },
]);

// const searchData = reactive({
//         fields: {
//           status: {
//             name: "状态",
//             type: "a-select",
//             options: [],
//             placeholder: "选择状态",
//             width: "120px",
//             fieldNames: {
//               label: "name",
//               value: "id",
//             },
//             value: "",
//           },
//           keyword: {
//               type: "a-input-search",
//               placeholder:'输入合同编号/合同名称/客户名称',
//               width: "300px",
//             },
//         },

//     });

const searchData = ref({
  searchButtons: [],
  operationButtons: [],
  fields: {
    status: {
      name: "产品分类",
      type: "a-select",
      options: [...wholeType, ...orderBOMType],
      placeholder: "选择状态",
      width: "120px",
      allowClear: true,
    },
    isLocked: {
      name: "锁定状态",
      type: "a-select",
      options: [...wholeType, ...orderBomIsLockedList],
      placeholder: "锁定状态",
      width: "120px",

      allowClear: true,
    },
    orderType: {
      name: "订单类型",
      type: "a-select",
      options: [...wholeType, ...orderTypeList],
      placeholder: "订单类型",
      width: "120px",
      // value: "",
      allowClear: true,
    },
    orderNo: {
      type: "a-input-search",
      placeholder: "输入订单号",
      width: "200px",
      allowClear: true,
    },
    rangeDate: {
      type: "a-range-picker",
      valueFormat: "YYYY-MM-DD",
      value: [],
      width: "240px",
    },
    bizBelongUser: {
      type: "a-input-search",
      placeholder: "业务归属人",
      width: "200px",
      allowClear: true,
    },
    customer: {
      type: "a-input-search",
      placeholder: "客户名称/编码",
      width: "200px",
      allowClear: true,
    },
    productMarketClass: {
      name: "产品市场分类",
      type: "a-select",
      options: [...wholeType, ...productMarketClassificationList],
      placeholder: "产品市场分类",
      width: "200px",

      allowClear: true,
    },
    materialNo: {
      type: "a-input-search",
      placeholder: "产品编码",
      width: "200px",
      allowClear: true,
    },
    materialName: {
      type: "a-input-search",
      placeholder: "产品名称",
      width: "200px",
      allowClear: true,
    },
    specification: {
      type: "a-input-search",
      placeholder: "产品规格",
      width: "200px",
      allowClear: true,
    },
    fileNumber: {
      type: "a-input-search",
      placeholder: "技术规格书",
      width: "200px",
      allowClear: true,
    },
  },
});

const { proxy } = getCurrentInstance();
const visibleDetail = ref(false);
const visibleAddContractReviewDrawer = ref(false);

const contractReviewId = ref("");
const data = ref([]),
  loading = ref(false);
const refAddContractReviewDrawer = ref(null);
const getList = async () => {
  loading.value = true;
  //分页信息
  let searchParam = {};
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  let { rangeDate } = toRaw(searchParam);
  if (rangeDate && rangeDate.length > 0) {
    searchParam.startTime = rangeDate[0] + " 00:00:00";
    searchParam.endTime = rangeDate[1] + " 23:59:59";
  }
  let result = await getBomMarketOrderPage({ ...pageParam.value }, searchParam);
  data.value = result.data;
  paginationProps.value.total = result.total;
  loading.value = false;
};
onBeforeMount(async () => {
  await getList();
});
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);

const onOpen = (valId) => {
  contractReviewId.value = valId;
  visibleAddContractReviewDrawer.value = true;
};

const onOpenDetail = (valId) => {
  router.push({ name: "OrderBomDetail", query: { id: valId } });
  // contractReviewId.value = valId;
  // visibleDetail.value = true;
};

// const rowClick = (record) => {
//   // return {
//   //   onClick: (event) => {
//   router.push({ name: "SupplierCreate", query: { id: record.id } });
//   //   },
//   // };
// };

async function onConfirm(record) {
  getList();
}
const onCancel = (record) => {
  // message.error('Click on No');
};
const exportLoading = ref(false);
const exportOrder = async () => {
  exportLoading.value = true;
  let searchParam = {};
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  let { rangeDate } = toRaw(searchParam);
  if (rangeDate && rangeDate.length > 0) {
    searchParam.startTime = rangeDate[0] + " 00:00:00";
    searchParam.endTime = rangeDate[1] + " 23:59:59";
    searchParam.rangeDate = undefined;
  } else {
    searchParam.rangeDate = undefined;
  }
  console.log("[ searchParam ] >", searchParam);
  let result = await exportOrderBom({
    ...searchParam,
  });
  const fileName = "订单物料详情导出.xlsx";
  exportExecl(fileName, result);
  exportLoading.value = false;
};
</script>
