<template>
  <div>
    <search :searchData="searchData" @search="refresh" style="display: none">
    </search>
    <mw-table
      :columns="columns"
      :data-source="schedulingData"
      :loading="loading"
      :rowKey="(record) => record.index"
      :rowSelection="{
        columnTitle: '选择',
        columnWidth: 80,
        type: 'radio',
        selectedRowKeys: selectedRowKeys,
        onChange: rowClick,
      }"
      hasPage
      @change="onTableChange"
      :scroll="{ x: 'max-content' }"
      :pageConfig="paginationProps"
    >
    </mw-table>
    <div v-if="schedulingDetailData?.length > 0">
      <div style="display: flex; justify-content: space-between">
        <div>交货日期： {{ deliveryTime }}</div>
        <mw-button
          title="新增"
          :font="'iconfont icon-xianxing-121'"
          class="mb-2"
          @click="showInOutCheckDrawer"
        ></mw-button>
      </div>
      <mw-table
        bordered
        :row-selection="rowSelection"
        :columns="columnsDetail"
        :data-source="cloneData"
        :scroll="{ x: 'max-content' }"
        @change="handlePageChange"
        :rowKey="(record) => record.index"
        hasPage
        :pageConfig="pagination"
      >
        <!-- :pagination="pagination" -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key == 'productName'">
            <div v-for="(item, index) in record.marketProductVOs" :key="index">
              <p
                :class="[
                  'customs',
                  { custom: index !== record.marketProductVOs.length - 1 },
                ]"
              >
                <!-- {{ item.productName }} -->
                {{ record.marketProductVOs.length }}
              </p>
            </div>
          </template>
          <template v-if="column.key == 'specification'">
            <div v-for="(item, index) in record.marketProductVOs" :key="index">
              <p
                :class="[
                  'customs',
                  { custom: index !== record.marketProductVOs.length - 1 },
                ]"
              >
                {{ item.specification }}
              </p>
            </div>
          </template>
          <template v-if="column.key == 'productNo'">
            <div v-for="(item, index) in record.marketProductVOs" :key="index">
              <p
                :class="[
                  'customs',
                  { custom: index !== record.marketProductVOs.length - 1 },
                ]"
              >
                {{ item.productNo }}
              </p>
            </div>
          </template>
          <template v-if="column.key == 'schedulingQuantity'">
            <div v-for="(item, index) in record.marketProductVOs" :key="index">
              <p
                :class="[
                  'customs',
                  { custom: index !== record.marketProductVOs.length - 1 },
                ]"
              >
                {{ item.schedulingQuantity }}
              </p>
            </div>
          </template>
          <template v-if="column.key == 'unSchedulingQuantity'">
            <div v-for="(item, index) in record.marketProductVOs" :key="index">
              <p
                :class="[
                  'customs',
                  { custom: index !== record.marketProductVOs.length - 1 },
                ]"
              >
                {{ item.unSchedulingQuantity }}
              </p>
            </div>
          </template>

          <template v-if="column.key == 'operate'">
            <mw-button title="详情"></mw-button> </template
        ></template>
      </mw-table>
    </div>
    <new-increase-drawer
      v-model:visible="inOutCheckVisible"
      @finish="getList"
      :selectedRowKeys="pendingProductionData"
    />
  </div>
</template>

<script setup>
import {
  ref,
  reactive,
  toRaw,
  defineEmits,
  onBeforeMount,
  computed,
} from "vue";
import { purchaseStatusFilters } from "@/common/constant.js";
import Search from "@/components/search/index.vue";
import { schedulingOrderStatistics } from "@/api/proSchedul/waitProductionSchedul.js";
import { usePagenation } from "@/common/setup";
import newIncreaseDrawer from "../newIncreaseDrawer.vue";
import _cloneDeep from "lodash/cloneDeep";
const schedulingData = ref([{}]);
const schedulingDetailData = ref([]);
const inOutCheckVisible = ref(false);
const loading = ref(false);
const deliveryTime = ref();
const pendingProductionData = ref();
const selectedRowKeysList = ref();
const cloneData = ref();
const searchData = reactive({
  fields: {
    status: {
      name: "采购状态",
      type: "a-select",
      options: purchaseStatusFilters,
      placeholder: "选择状态",
      width: "120px",
      value: "",
    },
    rangeDate: {
      type: "a-range-picker",
      valueFormat: "YYYY-MM-DD",
      value: [],
      width: "240px",
    },
    keyword: {
      type: "a-input-search",
      placeholder: "输入订单编号/编码搜索",
      width: "240px",
      allowClear: true,
    },
  },
});
const columns = ref([
  {
    title: "交货日期",
    key: "deliveryTime",
    dataIndex: "deliveryTime",
  },
  {
    title: "订单数量",
    key: "marketOrderQuantity",
    dataIndex: "marketOrderQuantity",
  },
  {
    title: "已排产",
    key: "schedulingQuantity",
    dataIndex: "schedulingQuantity",
  },
  {
    title: "剩余排产",
    key: "unSchedulingQuantity",
    dataIndex: "unSchedulingQuantity",
  },
]);
const pagination = ref({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
});
const columnsDetail = ref([
  {
    title: "订单编号",
    key: "marketOrderNo",
    dataIndex: "marketOrderNo",
    customCell: (value, index, column) => {
      return {
        rowSpan: value.marketOrderNoRowSpan,
      };
    },
  },
  {
    title: "订单名称",
    key: "marketOrderName",
    dataIndex: "marketOrderName",
    customCell: (value, index, column) => {
      return {
        rowSpan: value.marketOrderNameRowSpan,
      };
    },
  },
  {
    title: "产品名称",
    key: "materialName",
    dataIndex: "materialName",
  },
  {
    title: "交货日期",
    key: "deliveryTime",
    dataIndex: "deliveryTime",
  },
  {
    title: "产品规格",
    key: "specification",
    dataIndex: "specification",
  },
  {
    title: "产品编码",
    key: "materialNo",
    dataIndex: "materialNo",
  },
  {
    title: "已经排产数",
    key: "schedulingQuantity",
    dataIndex: "schedulingQuantity",
  },
  {
    title: "剩余排产数量",
    key: "unSchedulingQuantity",
    dataIndex: "unSchedulingQuantity",
  },
]);
//  合并单元格
const combineRow = (key) => {
  // debugger;
  let tableData = cloneData.value;
  for (var i = 0; i < tableData?.length; i++) {
    const item = tableData[i];
    let count = 1;
    // 开始数据
    const currents =
      pagination.value.current == 1
        ? (pagination.value.current - 1) * pagination.value.pageSize
        : pagination.value.pageSize * (pagination.value.current - 1);
    // 结束数据
    const page = currents + pagination.value.pageSize - 1;

    // 循环的数据
    for (let j = i + 1; j < tableData?.length; j++) {
      // 如果当前数据大于开始值,小于结束值得时候,才开始走判断

      if (i >= currents && i + 1 <= page) {
        // 如果是同一个值，往后递增
        if (item[key] == tableData[j][key]) {
          count++;
          // 往后相同的值都设为空单元格
          tableData[j][`${key}RowSpan`] = 0;
          // 只有同值第一个才设置合并的单元格数
          item[`${key}RowSpan`] = count;
          // 所有都是为同一个值的情况
          // 如果到了尾部，则循环结束
          if (j == tableData.length - 1) {
            return;
          }
        } else {
          // 指针跳转到下一个，从下一排开始
          i = j - 1;
          count = 1;
          tableData[j][`${key}RowSpan`] = 1;
          break;
        }
      } else {
        tableData[j][`${key}RowSpan`] = 1;
      }
    }
  }
  // cloneData.value = tableData;
};
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectedRowKeysList.value,
    selectedRows: pendingProductionData.value,
    onChange: (selectedRowKeys, selectedRows) => {
      selectedRowKeysList.value = selectedRowKeys;
      pendingProductionData.value = selectedRows;
    },
  };
});

const getList = async () => {
  loading.value = true;

  let result = await schedulingOrderStatistics({
    ...pageParam.value,
    // bizType: props.type,
  });
  schedulingData.value = result.data;
  paginationProps.value.total = result.total;
  loading.value = false;
};
// const { refresh, pageParam, paginationProps } = usePagenation(getList);
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);
function rowClick(val, item) {
  let record = item[0];
  console.log(record);
  pagination.value.current = 1;
  deliveryTime.value = record.deliveryTime;
  schedulingDetailData.value = _cloneDeep(record.marketOrderVOs);
  cloneData.value = record.marketOrderVOs;
  pagination.value.total = record.marketOrderVOs?.length || 0;
  combineRow("marketOrderNo");
  combineRow("marketOrderName");
  schedulingData.value.forEach((item, ind) => {
    if (item?.index == record?.index) {
      item.checkedValue = true;
    } else {
      item.checkedValue = false;
    }
  });
  selectedRowKeysList.value = [];
  pendingProductionData.value = [];
}
const showInOutCheckDrawer = () => {
  inOutCheckVisible.value = true;
};
onBeforeMount(() => {
  getList();
});

const handlePageChange = (page) => {
  if (!page?.current) return;
  pagination.value.current = page.current;
  pagination.value.pageSize = page.pageSize;
  pagination.value.total = page.total;
  combineRow("marketOrderNo");
  combineRow("marketOrderName");
};
</script>

<style lang="less" scoped>
// .custom {
//   border-bottom: 1px solid #f0f0f0;
// }
// .customs {
//   line-height: 30px;
// }
// :deep(.ant-table-cell) {
//   padding: 16px 0px !important;
//   text-align: center;
// }
</style>
