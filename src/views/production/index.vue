<script setup>
import { ref, onBeforeMount, reactive, toRaw } from "vue";
import Search from "@/components/search/index.vue";
import { productionStatus, orderTypeList } from "@/common/constant.js";
import newCreatePlanDrawer from "./newCreatePlanDrawer.vue";
import PlanDetailDrawer from "./PlanDetailDrawer.vue";
import { getPage, postNewPage, planExportExcel } from "@/api/plan.js";
import { usePagenation } from "@/common/setup/index.js";
import { useRoute, useRouter } from "vue-router";
import { list as getLineAllList } from "@/api/basicData/productionLine.js";
import { useUserStore } from "@/stores/user.js";
import productionDetailTab from "./productionDetailTab/productionDetailTab.vue";
import { exportExecl } from "@/utils/util.js";
const detailsVisibleSwitch = ref(false);
const exportLoading = ref(false);
const planId = ref();
const productLines = ref([]),
  addVisible = ref(false),
  spinning = ref(false),
  tableLoading = ref(false),
  planNo = ref(""),
  detailVisible = ref(false),
  router = useRouter(),
  route = useRoute(),
  store = useUserStore();
const columns = ref([
  {
    title: "计划名称",
    dataIndex: "planName",
    key: "planName",
  },
  {
    title: "产品/生产线",
    dataIndex: "materialOrProductLine",
    key: "materialOrProductLine",
  },

  {
    title: "销售订单号",
    dataIndex: "orderNo",
    key: "orderNo",
  },
  {
    title: "产品需求数 / 已完成 / 已报检 / 已合格 ",
    dataIndex: "demandAndFinished",
    key: "demandAndFinished",
  },
  {
    title: "计划进度",
    dataIndex: "status",
    key: "status",
  },
  {
    title: "生产负责人",
    dataIndex: "responsibleUser",
    key: "responsibleUser",
    ellipsis: true,
  },
  {
    title: "生产计划结束时间",
    dataIndex: "productionEndTime",
    key: "productionEndTime",
  },
  {
    title: "操作",
    dataIndex: "button",
    key: "button",
    fixed: "right",
  },
]);
const typeStatus = ref([
  { label: "全部", value: "" },
  { label: "进行中", value: "1" },
  { label: "已完成", value: "3" },
  { label: "已取消", value: "2" },
]);
const data = ref([]);
const searchData = ref({
  searchButtons: [],
  fields: {
    status: {
      name: "状态",
      type: "a-select",
      options: typeStatus,
      placeholder: "状态",
      width: "120px",
      allowClear: true,
      value: "1",
    },
    lineName: {
      name: "生产线",
      type: "a-select",
      options: [],
      placeholder: "全部生产线",
      width: "120px",
      allowClear: true,
      value: "",
    },
    keyword: {
      type: "a-input-search",
      placeholder: "输入生产计划编号/销售订单号/产品名称",
      width: "320px",
      allowClear: true,
    },
    // productName: {
    //   type: "a-input-search",
    //   placeholder: "输入负责人",
    //   width: "240px",
    // },
  },
});
const addNewPlan = () => {
  addVisible.value = true;
};
const getList = async () => {
  tableLoading.value = true;
  //分页信息
  let searchParam = {};
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  let { productionTime } = toRaw(searchParam);
  if (productionTime && productionTime.length > 0) {
    searchParam.startTime = productionTime[0] + " 00:00:00";
    searchParam.endTime = productionTime[1] + " 23:59:59";
  }
  //单个搜索信息
  // searchParam[searchData.filterData.value.filterBy] =
  //   searchData.filterData.value.filterValue;
  let result = await postNewPage(searchParam, { ...pageParam.value });
  data.value = result.data;
  // state.corpId = result.data.data.records[0].corpId;
  paginationProps.value.total = result.total;
  tableLoading.value = false;
};

const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);

const getLineList = async (status) => {
  let res = await getLineAllList({ onlyUnUsed: status });
  productLines.value = res.data.map((item) => {
    return {
      label: item.lineName,
      value: item.lineName,
    };
  });
  productLines.value.unshift({
    label: "全部生产线",
    value: "",
  });
  searchData.value.fields.lineName.options = productLines.value;
};

const exportOrg = async () => {
  exportLoading.value = true;
  let searchParam = {};
  //搜索信息
  for (const key in searchData.fields) {
    searchParam[key] = searchData.fields[key].value;
  }
  let { rangeDate } = toRaw(searchParam);
  if (rangeDate && rangeDate.length > 0) {
    searchParam.startTime = rangeDate[0] + " 00:00:00";
    searchParam.endTime = rangeDate[1] + " 23:59:59";
  }
  let result = await planExportExcel(searchParam, pageParam.value);
  const fileName = "生产计划.xlsx";
  exportExecl(fileName, result);
  exportLoading.value = false;
};
onBeforeMount(async () => {
  await getLineList(false);
  getList();
});
function rowClick(record) {
  record.name = "改变后的计划" + record.id;
  planId.value = record.id;
  planNo.value = record.planNo;
  detailsVisibleSwitch.value = true;
  // router.push({
  //   path: "detail",
  //   query: { planNo: record.planNo },
  // });
}
function finish(params) {
  getList();
}
</script>
<template>
  <div v-if="store.$state.cacheObject">
    <search :searchData="searchData" @search="refresh">
      <mw-button
        title="新增"
        :font="'iconfont icon-xianxing-121'"
        @click="addNewPlan"
        v-permission="'production:plan:add'"
      ></mw-button>
      <mw-button
        title="导出Excel"
        @click="exportOrg"
        :loading="exportLoading"
        v-permission="'production:plan:exportExcel'"
      ></mw-button>
    </search>
    <mw-table
      class="leading-5.5"
      :columns="columns"
      :data-source="data"
      :rowKey="(record) => record.id"
      :loading="tableLoading"
      hasPage
      :pageConfig="paginationProps"
      @change="onTableChange"
      :scroll="{ x: 'max-content' }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex == 'planName'">
          <div class="text-title overflow" :title="record.planName">
            计划名称： {{ record.planName }}
          </div>
          <div class="secondary-color">
            编码：{{ record.planNo }}
            <span v-if="record.isDeferred == 1" class="ml-2 tag" style="">{{
              "计划延期"
            }}</span>
          </div>
          <div class="secondary-color"></div>
        </template>
        <template v-if="column.dataIndex == 'materialOrProductLine'">
          <a-tooltip>
            <!-- <div class="w-52 truncate">{{ text }}</div>
            <template #title>{{ text }}</template> -->
            <div class="w-52 truncate">
              {{ record.materialNo }} / {{ record.materialName }} /{{
                record.specification
              }}
            </div>
            <div class="mutiLineOverflow" :title="record.lineName">
              {{ record.lineName }}
            </div>
            <template #title>
              {{ record.materialNo }} / {{ record.materialName }} /{{
                record.specification
              }}</template
            >
          </a-tooltip>
        </template>
        <template v-if="column.dataIndex == 'orderNo'">
          <div>{{ record.orderNo }}</div>
          <dictionary
            :statusOptions="orderTypeList"
            :value="record.orderType"
          />
        </template>

        <template v-if="column.dataIndex == 'demandAndFinished'">
          <div class="">
            {{ record.requiredQuantity }} / {{ record.completedQuantity }} /
            {{ record.testQuantity }} / {{ record.passQuantity }}
          </div>
        </template>
        <template v-if="column.dataIndex == 'status'">
          <div class="">
            <dictionary
              :statusOptions="productionStatus"
              :value="record.status"
              :statusExtend="record.statusExtend"
              isBackgroundColor
            />
          </div>
        </template>
        <template v-if="column.dataIndex == 'responsibleUser'">
          <div class="">
            {{ record.responsibleUser }}
          </div>
        </template>
        <template v-if="column.dataIndex == 'createTime'">
          <div class="">
            {{ record.createTime }}
          </div>
        </template>
        <template v-if="column.key == 'button'">
          <mw-button title="详情" @click="rowClick(record)"></mw-button>
        </template>
      </template>
    </mw-table>
  </div>

  <empty name="Production" v-else />
  <!-- <create-plan-drawer
    v-model:visible="addVisible"
    @finish="finish"
    v-model:planNo="planNo"
  /> -->
  <new-create-plan-drawer v-model:visible="addVisible" @finish="finish" />

  <!--   v-model:planNo="planNo"-->
  <plan-detail-drawer
    v-model:visible="detailVisible"
    @finish="finish"
    v-model:planNo="planNo"
  />

  <productionDetailTab
    v-model:visible="detailsVisibleSwitch"
    @finish="finish"
    :planNo="planNo"
    :planId="planId"
  ></productionDetailTab>
</template>
<style lang="less" scoped>
:deep(.ant-form-item) {
  margin-bottom: 16px;
}
:deep(.ant-table-cell) {
  vertical-align: top;
}
.tag {
  font-size: 12px;
  background: rgba(255, 77, 79, 0.2);
  color: rgb(255, 77, 79);
  padding: 2px 8px;
  border-radius: 2px;
}
</style>
