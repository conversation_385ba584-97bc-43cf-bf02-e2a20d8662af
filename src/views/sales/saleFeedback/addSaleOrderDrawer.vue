<template>
  <mw-drawer
    :visible="visible"
    @close="onClose()"
    destroyOnClose="true"
    width="35%"
    :customTitle="
      props.refundType == 'detail'
        ? '收退款详情'
        : props.id
        ? '编辑' + `&nbsp;&nbsp;&nbsp;单据编号：` + formData?.receiptNumber
        : '新增收退款'
    "
  >
    <template #header>
      <mw-button
        @click="formSubmit"
        :loading="submitLoading"
        v-if="props.refundType !== 'detail'"
        >{{ refundType == "edit" ? "保存并提交" : "确定" }}</mw-button
      >
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :colon="false"
      labelAlign="center"
      :wrapper-col="{ span: 15 }"
      :label-col="{ style: { width: '100px' } }"
    >
      <a-form-item label="客户" name="customerId" required>
        <a-select
          placeholder="请选择客户"
          v-model:value="formData.customerId"
          :options="postCustomerPageData"
          optionFilterProp="customerName"
          :fieldNames="{ label: 'customerName', value: 'id' }"
          style="width: 100%"
          allow-clear
          showSearch
          :disabled="props.refundType == 'detail'"
          @change="handleChange"
        >
        </a-select>
      </a-form-item>
      <a-form-item label="业务员">
        <!-- <a-input v-model:value="formData.customerManagerName"></a-input> -->
        <a-input
          v-model:value="formData.customerManagerName"
          placeholder="业务员"
          disabled
        />
      </a-form-item>
      <a-form-item label="业务部门">
        <a-input
          v-model:value="formData.customerManagerBelongDeptName"
          placeholder="业务部门"
          disabled
        />
      </a-form-item>
      <a-form-item label="" name="isContract" required>
        <a-checkbox
          v-model:checked="formData.isContract"
          class="ml-24"
          @change="changeIsContract"
          :disabled="props.refundType == 'detail'"
          >关联合同</a-checkbox
        >
      </a-form-item>
      <a-form-item
        v-if="formData.isContract == 1"
        label="合同"
        name="contractId"
        :required="formData.isContract"
      >
        <a-select
          placeholder="请选择合同"
          v-model:value="formData.contractId"
          :options="contractData"
          optionFilterProp="contractName"
          :fieldNames="{ label: 'contractName', value: 'contractId' }"
          style="width: 100%"
          allow-clear
          showSearch
          :disabled="!formData.isContract || props.refundType == 'detail'"
        >
        </a-select>
      </a-form-item>
      <a-form-item label="收款类型" name="payeeType" required>
        <!-- <a-radio-group
          v-model:value="formData.payeeType"
          :options="collectionType"
          :disabled="props.refundType == 'detail'"
        /> -->
        <radioGroup
          v-model:value="formData.payeeType"
          :options="collectionType"
          :disabled="props.refundType == 'detail'"
        >
        </radioGroup>
      </a-form-item>

      <a-form-item label="结算方式" required name="settlementType">
        <!-- <a-radio-group
          v-model:value="formData.settlementType"
          :options="settlementType"
          @change="changeContract"
          :disabled="props.refundType == 'detail'"
        /> -->
        <radioGroup
          v-model:value="formData.settlementType"
          :options="settlementType"
          @change="changeContract"
          :disabled="props.refundType == 'detail'"
        ></radioGroup>
      </a-form-item>
      <a-form-item
        label="票据号"
        v-if="formData.settlementType == 2 || formData.settlementType == 6"
      >
        <a-input
          v-model:value="formData.billNumber"
          placeholder="票据号"
          :disabled="props.refundType == 'detail'"
        />
      </a-form-item>
      <a-form-item label="银行账户" name="bankCode" required>
        <a-select
          :disabled="props.refundType == 'detail'"
          v-model:value="formData.bankCode"
          :options="blankList"
          placeholder="请选择银行账户"
          allow-clear
        />
      </a-form-item>
      <a-form-item label="收款金额" required name="payeeAmount">
        <a-input-number
          class="w-full"
          v-model:value="formData.payeeAmount"
          placeholder="收款金额"
          :disabled="props.refundType == 'detail'"
          :stringMode="true"
        />
      </a-form-item>
      <a-form-item label="币种" required name="currencyType">
        <a-select
          :disabled="props.refundType == 'detail'"
          v-model:value="formData.currencyType"
          :options="dictTypeData"
          placeholder="请选择币种"
          :fieldNames="{
            label: 'dictLabel',
            value: 'dictValue',
          }"
        />
      </a-form-item>
      <a-form-item
        label="收款日期"
        required
        name="payeeTime"
        :wrapper-col="{ span: 10, offset: 0 }"
      >
        <!-- <a-input v-model:value="formData.invoiceTime" /> -->
        <a-date-picker
          v-model:value="formData.payeeTime"
          style="width: 100%"
          valueFormat="YYYY-MM-DD"
          :disabled="props.refundType == 'detail'"
        />
      </a-form-item>
      <a-form-item
        v-if="formData.settlementType == '5'"
        label="其他"
        required
        name="other"
      >
        <a-input
          v-model:value="formData.other"
          placeholder="其他"
          :disabled="props.refundType == 'detail'"
        />
      </a-form-item>

      <a-form-item label="到账信息" required name="accountInfo">
        <a-input
          v-model:value="formData.accountInfo"
          placeholder="到账信息"
          :disabled="props.refundType == 'detail'"
        />
      </a-form-item>
      <a-form-item label="收款凭证" required>
        <form-upload
          v-if="props.refundType != 'detail'"
          v-model:value="payeeProofFile"
          sence="delivery"
          :fileTypes="[]"
          :fileSize="100"
          hasDownLoad
          :showDeletdPopup="true"
        ></form-upload>
        <div
          v-else
          class="overflow"
          v-for="(item, index) in payeeProofFile"
          :key="index"
        >
          <i
            class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
            style="color: #959ec3"
          ></i>
          <a
            :href="item.fileVisitUrl"
            :title="item.fileName"
            @click="downFile(item.fileVisitUrl, item.fileName)"
            target="_blank"
            class="underline"
            style="color: #959ec3"
            >{{ item.fileName }}
          </a>
        </div>
      </a-form-item>
    </a-form>
  </mw-drawer>
</template>
<script setup>
import {
  ref,
  onBeforeMount,
  onMounted,
  reactive,
  toRaw,
  getCurrentInstance,
  computed,
  defineProps,
  defineEmits,
  watch,
} from "vue";
import { collectionType, settlementType } from "@/common/constant.js";
import FormUpload from "@/components/form-upload.vue";
import { postCustomerPage } from "@/api/proSchedul/scheduling.js"; //客户信息
import {
  postNoSaleOrderList,
  postReceiptAdd,
  postInvoiceDetail, //详情
  queryCustomerAndContract,
  postReceiptDetail,
  getBankList,
  dictDataType,
} from "@/api/sales/index.js";
import { downFile } from "@/common/setup/index.js";
const postNoContractReviewListOptions = ref([]);
const emit = defineEmits(["update:visible", "finish"]);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    required: true,
  },
  refundType: {
    type: String,
    required: true,
  },
});
const dictTypeData = ref([]);
const blankList = ref([]);
const { proxy } = getCurrentInstance();
const postCustomerPageData = ref();
const formRef = ref();
const submitLoading = ref(false);
const payeeProofFile = ref([]);
const today = new Date(); // 获取当前日期
const formData = ref({
  isContract: false,
  payeeType: undefined,
  settlementType: undefined,
  // marketOrderId: "1",
  other: undefined,
  payeeAmount: undefined,
  accountInfo: undefined,
  payeeTime: `${today.getFullYear()}-${("0" + (today.getMonth() + 1)).slice(
    -2
  )}-${("0" + today.getDate()).slice(-2)}`,
  bankCode: void 0,
  billNumber: void 0,
  receiptNumber: "",
  contractId: undefined,
});
const contractData = ref();
const rules = reactive({
  // orderId: [
  //   {
  //     required: true,
  //     trigger: "blur",
  //     message: "请选择关联-销售订单",
  //   },
  // ],
});
const changeContract = () => {
  formData.value.billNumber = void 0;
};
const handleChange = (e, val) => {
  Object.assign(formData.value, {
    customerManagerId: val.customerManagerId || void 0,
    customerManagerName: val.customerManagerName || "无",
    customerManagerBelongDeptId: val.customerManagerBelongDeptId || void 0,
    customerManagerBelongDeptName: val.customerManagerBelongDeptName || "无",
  });
  contractData.value = val.contracts.map((item, index) => {
    item.contractName = item.contractName + " / " + item.contractNo;
    return item;
  });
  formData.value.contractId = undefined;
};
const queryCustomerAndContractList = async () => {
  let res = await queryCustomerAndContract({});
  postCustomerPageData.value = res.data;
  if (props.refundType == "detail" || props.refundType == "edit") {
    let data = res.data.filter((item) => item.id == formData.value.customerId);
    contractData.value = data[0].contracts.map((item, index) => {
      item.contractName = item.contractName + " / " + item.contractNo;
      return item;
    });
  }
};
const changeIsContract = (e) => {
  if (!e.target.checked) {
    formData.value.contractId = undefined;
    formData.isContract = 0;
  } else {
    formData.isContract = 1;
  }
};
function onClose() {
  emit("update:visible", false);
  formRef.value.resetFields();
  formData.payeeType = undefined;
  formData.payeeTime = undefined;
  formData.settlementType = undefined;
  formData.other = undefined;
  payeeProofFile.value = [];
  formData.payeeAmount = undefined;
  formData.accountInfo = undefined;
  formData.value.contractId = void 0;
  contractData.value = [];
  formData.value.customerManagerId = void 0;
  formData.value.customerManagerName = void 0;
  formData.value.customerManagerBelongDeptId = void 0;
  formData.value.customerManagerBelongDeptName = void 0;
  formData.value.billNumber = void 0;
}
const postCustomerPageList = async () => {
  let res = await postCustomerPage();
  postCustomerPageData.value = res.data;
};

// 关联-销售合同
const postNoContractReview = async () => {
  let param = {};
  let res = await postNoSaleOrderList(param);
  postNoContractReviewListOptions.value = res.data;
};

// postInvoiceDetail详情接口
const postInvoiceDetails = async (val) => {
  let res = await postReceiptDetail({ receiptId: val });
  for (const key in formData.value) {
    formData.value[key] = res.data[key];
    formData.value.currencyType = res.data.currencyType;
    formData.value.contractId = String(res.data.contractId || "");
    formData.value.customerId = String(res.data.customerId);
    formData.value.isContract = res.data.isContract == 1 ? true : false;
    formData.value.payeeType = String(res.data.payeeType);
    formData.value.settlementType = String(res.data.settlementType);
    payeeProofFile.value = [res.data.payeeProofFile];
    formData.value.customerManagerName = res.data.customerManagerName
      ? res.data.customerManagerName
      : "无";
    formData.value.customerManagerBelongDeptName = res.data
      .customerManagerBelongDeptName
      ? res.data.customerManagerBelongDeptName
      : "无";
  }
};
// 确认新增
const formSubmit = async () => {
  if (!formData.value.customerManagerBelongDeptId) {
    proxy.$message.error("关联销售部门不能为空！");
    return false;
  }
  formRef.value.validate().then(async () => {
    try {
      submitLoading.value = true;
      formData.payeeProofFile = payeeProofFile.value[0];
      if (formData.value.customerManagerName == "无") {
        formData.value.customerManagerName = void 0;
      }
      if (formData.value.customerManagerBelongDeptName == "无") {
        formData.value.customerManagerBelongDeptName = void 0;
      }
      let param = {
        ...formData.value,
        isContract: formData.isContract == true ? 1 : 0,
        payeeProofFile: payeeProofFile.value[0],
      };

      if (props.id) {
        param.id = props.id;
      }
      let res = await postReceiptAdd(param);
      if (res.code == 200) {
        proxy.$message.success("操作成功");
        onClose();
      }
      submitLoading.value = false;
      emit("finish");
    } catch (error) {
      submitLoading.value = false;
    }
  });
};

const getBank = async () => {
  const { code, data } = await getBankList();
  if (code == 200) {
    blankList.value = data.map((item) => {
      return {
        label: item.bankCode + "-" + item.bankName,
        value: item.bankCode,
      };
    });
  }
};
// 字典查询
const getDictDataType = async () => {
  let dictType = "market_currency_type";
  let res = await dictDataType(dictType);
  dictTypeData.value = res.data;
};
watch(
  () => props.visible,
  async (val) => {
    if (val) {
      if (props.id) {
        await postInvoiceDetails(props.id);
      }
      getBank();
      await postNoContractReview();
      await queryCustomerAndContractList();
      await getDictDataType();
    }
  }
);
</script>
<style lang="less" scoped>
:deep(.ant-form label) {
  padding: 10px;
}
</style>
