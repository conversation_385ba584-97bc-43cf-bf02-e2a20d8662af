<template>
  <mw-drawer
    :visible="visible"
    @close="onClose()"
    width="80%"
    :customTitle="
      props.type == 'det'
        ? '合同评审详情'
        : props.type == 'edit'
        ? '编辑合同评审'
        : '新增合同评审'
    "
  >
    <template #header>
      <mw-button
        :title="'提交'"
        v-if="props.type !== 'det'"
        @click="formSubmit"
        :loading="loading"
      ></mw-button>

      <!-- <a-button
        shape="round"
        v-if="props.id"
        type="primary"
        @click="exportOrg(record)"
        v-permission="'market:contract:exportExcel'"
      >
        导出
      </a-button> -->
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :colon="false"
      labelAlign="left"
      :label-col="{ span: 7, offset: 1 }"
    >
      <a-row>
        <a-col :span="12">
          <a-form-item label="合同分类" name="contractClassify" required>
            <radioGroup
              v-model:value="formData.contractClassify"
              :options="contractClassifyList"
              @change="changeContract"
              :disabled="props.type == 'det' ? true : false"
            ></radioGroup>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="关联-订单合同"
            name="relationContractId"
            :required="formData.invoiceType == 'Normal' ? true : false"
            v-if="formData.contractClassify == 'Supplement'"
          >
            <a-select
              placeholder="关联-订单合同"
              :disabled="props.type == 'det' ? true : false"
              :options="postNoContractReviewListOptions"
              v-model:value="formData.relationContractId"
              :filter-option="filterOption"
              @change="handleChange"
              :field-names="{
                label: 'contractName',
                value: 'id',
              }"
              show-search
              allowClear
            ></a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12" v-if="props.type == 'det'">
          <a-form-item label="合同编号" required>
            <a-input
              :disabled="props.type == 'det' ? true : false"
              v-model:value="formData.contractNo"
            />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="单据类型" name="invoiceType" required>
            <!-- <a-radio-group
              :disabled="props.type == 'det' ? true : false"
              v-model:value="formData.invoiceType"
              :options="invoiceTypeList"
              @change="changeInvoiceType"
            /> -->
            <radioGroup
              v-model:value="formData.invoiceType"
              :options="invoiceTypeList"
              @change="changeInvoiceType"
              :disabled="props.type == 'det' ? true : false"
            ></radioGroup>
          </a-form-item>
        </a-col>
        <a-col :span="12" v-if="props.type == 'det'">
          <a-form-item label="订单下单状态" required>
            <a-tag color="orange" v-if="formData.status == '0'">待下单</a-tag>
            <a-tag color="green" v-if="formData.status == '1'">已下单</a-tag>
            <a-tag color="red" v-if="formData.status == '2'">已作废</a-tag>
            <a-tag color="orange" v-if="formData.status == '3'">待补充</a-tag>
            <a-tag color="orange" v-if="formData.status == '4'">部分下单</a-tag>
            <a-tag color="blue" v-if="formData.status == '7'">待审批</a-tag>
            <a-tag color="green" v-if="formData.status == '8'">审批通过</a-tag>
            <a-tag color="error" v-if="formData.status == '9'"
              >审批不通过</a-tag
            >
            <a-tag color="red" v-if="formData.status == '10'">审批撤销</a-tag>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="合同管理人" name="belongUserId" required>
            <a-select
              :disabled="props.type == 'det' ? true : false"
              placeholder="请选择合同管理人"
              :options="userListOptions"
              v-model:value="formData.belongUserId"
              optionFilterProp="nickName"
              :field-names="{
                label: 'nickName',
                value: 'userId',
              }"
              show-search
              @change="handleManagerChange"
            >
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="合同名称" name="contractName" required>
            <a-input
              :disabled="props.type == 'det' ? true : false"
              v-model:value="formData.contractName"
              placeholder="请输入合同名称"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="业务类别" name="contractType" required>
            <a-select
              :disabled="props.type == 'det' ? true : false"
              placeholder="请选择业务类别"
              :options="AllCusinessTypes"
              v-model:value="formData.contractType"
              optionFilterProp="label"
              @change="handleContractTypeChange"
              :field-names="{
                label: 'label',
                value: 'value',
              }"
              show-search
            >
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="客户代码" name="customerId" required>
            <a-select
              :disabled="props.type == 'det' ? true : false"
              placeholder="请选择客户代码"
              :options="customerPageList"
              v-model:value="formData.customerId"
              optionFilterProp="label"
              :field-names="{
                label: 'label',
                value: 'value',
              }"
              show-search
              @change="customerChange"
            >
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :span="12" v-if="props.type == 'det'">
          <a-form-item label="客户名称" name="customerName">
            <a-input v-model:value="formData.customerName" disabled></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="产品类别"
            name="productMarketClassification"
            :required="formData.invoiceType == 'Normal' ? true : false"
          >
            <a-select
              :disabled="props.type == 'det' ? true : false"
              placeholder="请选择产品类别"
              :options="AllProductTypes"
              v-model:value="formData.productMarketClassification"
              optionFilterProp="label"
              @change="handleProductTypeChange"
              :field-names="{
                label: 'label',
                value: 'value',
              }"
              show-search
            >
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :span="12" v-if="props.type !== 'det'">
          <div>
            <a-form-item label="合同编号" name="contractNo">
              <!-- <a-input
                disabled
                v-model:value="formData.contractNo"
                placeholder="合同编号"
              /> -->
              {{ formData.contractNo }}
              <br />
              <div class="text-xs opacity-80">
                本合同编号为预计编号，以提交后生成的合同编号为准
              </div>
            </a-form-item>
            <a-form-item label=" " name="contractNo"> </a-form-item>
          </div>
        </a-col>
        <a-col :span="12">
          <a-form-item label="使用国家" name="useCountry" required>
            <!-- <a-radio-group
              :disabled="props.type == 'det' ? true : false"
              v-model:value="formData.useCountry"
              :options="useCountryList"
            /> -->
            <radioGroup
              v-model:value="formData.useCountry"
              :options="useCountryList"
              :radioDisabled="props.type == 'det'"
              @change="changeUseCountry"
            ></radioGroup>
          </a-form-item>
          <a-form-item
            label="国外国家"
            v-if="formData.useCountry == 1"
            name="foreignCountry"
            required
          >
            <a-input
              v-model:value="formData.foreignCountry"
              placeholder="国家"
              :disabled="props.type == 'det' ? true : false"
            />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="合同附件">
            <!-- <form-upload
              v-else
              v-model:value="formData.file"
              sence="delivery"
              :fileTypes="[]"
              :fileLimit="99999999"
              :fileSize="100"
              hasDownLoad
            ></form-upload> -->
            <div v-if="props.type == 'det'">
              <div
                class="overflow"
                v-for="(item, index) in formData.file"
                :key="index"
              >
                <i
                  class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
                  style="color: #959ec3"
                ></i>
                <a
                  :title="item.fileName"
                  @click="downFile(item.fileVisitUrl, item.fileName)"
                  target="_blank"
                  class="underline"
                  style="color: #959ec3"
                  >{{ item.fileName }}
                </a>
              </div>
            </div>

            <form-upload-contract
              v-else
              v-model:value="formData.file"
              sence="article"
              :fileTypes="[]"
              :fileSize="100"
              :readonly="butType == 'detail'"
              hasDownLoad
              :fileLName="true"
              :fileLimit="9999"
              :detailType="'1'"
              @del="delFile"
              :delShow="true"
            >
            </form-upload-contract>
          </a-form-item>
        </a-col>
        <a-col
          :span="24"
          v-if="
            formData.invoiceType === 'Normal' ||
            (props.id && formData.invoiceType === 'Prefetch number')
          "
        >
          <div style="margin-left: -30px">
            <a-form-item
              label="产品明细"
              :required="formData.invoiceType == 'Normal' ? true : false"
              name="relationProductList"
            >
              <mw-button
                :title="'新增'"
                v-if="props.type !== 'det'"
                @click="handleAdd"
                class="mb-2"
                style="margin-left: -22%"
              ></mw-button>
            </a-form-item>
          </div>
          <mw-table
            :scroll="{ x: 'max-content' }"
            :data-source="formData.relationProductList"
            :columns="columnsRelationProduct"
            :rowKey="(index) => index"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.dataIndex === 'materialId'">
                <div v-if="props.type == 'det'">
                  {{ record.materialNo }}/{{ record.materialName }}/{{
                    record.specification
                  }}
                </div>
                <a-select
                  v-else
                  v-model:value="record.materialId"
                  show-search
                  :filter-option="filterOptionM"
                  style="width: 100%"
                  :default-active-first-option="false"
                  :options="allMaterialList"
                  @change="(val, option) => onProductData(val, option, record)"
                  @search="handleSearch"
                  placeholder="请输入物料编码/名称"
                ></a-select>
              </template>
              <template v-if="column.dataIndex === 'bomName'">
                <a @click="gotoBom(record.bomNo)" style="color: #1890ff">{{
                  record.bomName
                }}</a>
              </template>
              <template v-if="column.dataIndex === 'file'">
                <div
                  class="overflow"
                  v-for="(item, ind) in record.file"
                  :key="ind"
                >
                  <i
                    class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
                    style="color: #959ec3"
                  ></i>
                  <a
                    :title="item.fileName"
                    @click="downFile(item.fileVisitUrl, item.fileName)"
                    target="_blank"
                    class="underline"
                    style="color: #959ec3"
                    >{{ item.fileName }}
                  </a>
                </div>
              </template>

              <template v-if="column.dataIndex === 'use'">
                <div v-if="props.type == 'det'">{{ record.use }}</div>
                <a-input v-else v-model:value="record.use" placeholder="备注" />
              </template>
              <template v-if="column.dataIndex === 'unitPrice'">
                <div v-if="props.type == 'det'">{{ record.unitPrice }}</div>
                <a-input-number
                  v-else
                  v-model:value="record.unitPrice"
                  :formatter="formatter2"
                  placeholder="含税单价"
                  :stringMode="true"
                />
              </template>

              <template v-if="column.dataIndex === 'quantity'">
                <div v-if="props.type == 'det'">{{ record.quantity }}</div>
                <a-input-number
                  v-else
                  v-model:value="record.quantity"
                  placeholder="数量"
                  :formatter="formatter2"
                  :stringMode="true"
                />
              </template>
              <template v-if="column.dataIndex === 'taxRate'">
                <div v-if="props.type == 'det'">{{ record.taxRate }}</div>
                <a-select
                  class="w-full"
                  v-else
                  :disabled="props.type == 'det' ? true : false"
                  v-model:value="record.taxRate"
                  :options="taxRateList"
                  placeholder="请选择税率"
                />
                <!-- <a-input-number
                  v-else
                  class="w-30"
                  v-model:value="record.taxRate"
                  placeholder="税率"
                  addon-after="%"
                  :stringMode="true"
                />
                /> -->
              </template>

              <template v-if="column.dataIndex === 'amount'">
                <div v-if="record.unitPrice && record.quantity">
                  {{
                    (record.amount = parseFloat(
                      (record.unitPrice * record.quantity).toFixed(6)
                    ))
                  }}
                </div>
                <div v-else>0</div>
              </template>
              <template
                v-if="column.dataIndex === 'operation' && props.type !== 'det'"
              >
                <a-popconfirm
                  title="确定是否删除"
                  @confirm="onDelete(record, index)"
                >
                  <mw-button :title="'删除'" danger></mw-button>
                </a-popconfirm>
              </template>
            </template>
          </mw-table>
        </a-col>
        <a-col :span="12" class="mt-5">
          <a-form-item label="其他备注" name="remark">
            <a-textarea
              :disabled="props.type == 'det' ? true : false"
              style="width: 100%"
              v-model:value="formData.remark"
              placeholder="其他备注"
              allow-clear
            />
          </a-form-item>
        </a-col>
        <a-col :span="12" v-if="formData.invoiceType === 'Normal'" class="mt-5">
          <a-form-item label="合同金额" required>
            <a-input disabled v-model:value="formData.totalContractAmount" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="币种" required name="currencyType">
            <a-select
              :disabled="props.type == 'det' ? true : false"
              v-model:value="formData.currencyType"
              :options="dictTypeData"
              placeholder="请选择币种"
              :fieldNames="{
                label: 'dictLabel',
                value: 'dictValue',
              }"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </mw-drawer>
</template>
<script setup>
import {
  ref,
  reactive,
  getCurrentInstance,
  computed,
  defineProps,
  defineEmits,
  watch,
} from "vue";

import { postCustomerPage, productList } from "@/api/proSchedul/scheduling.js"; //客户信息
import { formatter2 } from "@/common/validate.js";
import {
  contractClassifyList,
  invoiceTypeList,
  autoGenerationCodeFlagList,
  contractTypeList,
  productMarketClassificationList,
  useCountryList,
  taxRateList,
  businessTypes,
  productTypes,
} from "@/common/constant.js";

import FormUploadContract from "@/components/form-upload-contract.vue";
import { getAllUser } from "@/api/system/user.js";
import { message } from "ant-design-vue";
import _cloneDeep from "lodash/cloneDeep";
import { useUserStore } from "@/stores/user.js";
import { useRoute, useRouter } from "vue-router";
import { downFile } from "@/common/setup/index.js";
import { AllList, AllPage } from "@/api/basicData/material.js";
import { getContractNumber } from "@/api/purchase/order.js";

import { debounce } from "lodash";

// 下面导出暂时注释
// import { contractExportExcel } from "@/api/sales/index.js";
// import { exportExecl } from "@/utils/util.js";
import {
  postContractReviewAdd,
  getContractReviewDetail,
  postContractReviewUpdate,
  postNoContractReviewList,
  dictDataType,
  getCustomerManager,
} from "@/api/sales/index.js";
import { get } from "lodash";
const router = useRouter();
const store = useUserStore();
// store.user.tenantId
const emit = defineEmits(["update:visible", "finish", "update:value"]);
const productListData = ref([]);
const postNoContractReviewListOptions = ref([]);
const dictTypeData = ref([]);
const exportLoading = ref(false);
const AllProductTypes = computed(() => {
  if (props.type == "det") {
    return [...productMarketClassificationList, ...productTypes];
  } else {
    return productTypes;
  }
});
const AllCusinessTypes = computed(() => {
  if (props.type == "det") {
    return [...contractTypeList, ...businessTypes];
  } else {
    return businessTypes;
  }
});
const materiaSelect = {
  isMarket: 1,
};
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    required: true,
  },
  valItem: {
    type: Object,
    required: {},
  },
});
const { proxy } = getCurrentInstance();
const formRef = ref();
const customerPageList = ref([]);
const loading = ref(false);
const today = new Date(); // 获取当前日期
const userListOptions = ref();
const rowSelection = computed(() => {
  let total = 0;
  formData.relationProductList.forEach((acc, cur) => {
    if (acc.unitPrice && acc.quantity) {
      total += acc.amount;
    }
  });
  return parseFloat(total.toFixed(6));
});
const formData = reactive({
  contractClassify: "Normal",
  status: "",
  invoiceType: "Normal",
  useCountry: 0,
  totalContractAmount: rowSelection,
  relationProductList: [],
  reviewTime: `${today.getFullYear()}-${("0" + (today.getMonth() + 1)).slice(
    -2
  )}-${("0" + today.getDate()).slice(-2)}`,
  file: [],
});

const columnsRelationProduct = ref([
  {
    title: "产品名称",
    dataIndex: "materialId",
    width: 380,
  },
  {
    title: "技术协议附件",
    dataIndex: "file",
  },
  {
    title: "含税单价",
    dataIndex: "unitPrice",
  },
  {
    title: "数量",
    dataIndex: "quantity",
  },
  {
    title: "税率",
    dataIndex: "taxRate",
  },
  {
    title: "金额",
    dataIndex: "amount",
  },
  // 功能不删,暂时隐藏
  // {
  //   title: "产品规格",
  //   dataIndex: "specification",
  //   width: "150px",
  // },
  // {
  //   title: "协议编号",
  //   dataIndex: "fileNumber",
  //   width: "150px",
  // },

  {
    title: "备注",
    dataIndex: "use",
  },
  // {
  //   title: "是否需要调试",
  //   dataIndex: "isNeedDebugging",
  // },
  {
    title: "操作",
    dataIndex: "operation",
  },
]);
const rules = reactive({
  materialIdOrProductId: [
    {
      required: true,
      message: "请选择" + props.name,
      trigger: "blur",
    },
  ],
});
const customerChange = async (e, val) => {
  let res = await getCustomerManager({ customerId: val.id });
  const result = userListOptions.value.some(
    (item) => item.userId == res.data?.userId
  );
  if (result) {
    formData.belongUserId = Number(res.data?.userId) || void 0;
  } else {
    formData.belongUserId = void 0;
  }

  // 客户代码变化后，更新合同编号
  updateContractNo();
};
const filterOption = (input, option) => {
  return (
    option.contractName?.toLowerCase().indexOf(input.toLowerCase()) >= 0 ||
    option.contractNo?.toLowerCase().indexOf(input.toLowerCase()) >= 0
  );
};
const changeUseCountry = () => {
  formData.foreignCountry = void 0;
};
const getUserList = async () => {
  let result = await getAllUser();
  userListOptions.value = result.data;
};
const changeContract = (e) => {
  formData.relationContractId = void 0;
};
const changeInvoiceType = (e) => {
  //6/26测试提出要去掉
  // formData.productMarketClassification = "";
  formData.reviewTime = void 0;
  formData.deliveryTime = void 0;
  formData.relationProductList = [];
};
const debouncedSearch = debounce(async (e) => {
  console.log("[ e ] >", e);
  await getAllMaterialList(e);
  //
}, 300);
const handleSearch = async (e) => {
  debouncedSearch(e);
};
const onProductData = (e, val, row) => {
  console.log("[ e ] >", e);
  row.bomName = val.bomName;
  row.fileNumber = val.fileNumber;
  row.specification = val.specification;
  row.isNeedDebugging =
    val.isNeedDebugging == 1 ? "是" : val.isNeedDebugging == 0 ? "否" : "";
  row.use = val.use;
  if (val.file && val.file.length > 0) {
    row.file = val.file;
  } else {
    row.file = [];
  }
  row.materialName = val.materialName;
  row.materialNo = val.materialNo;
};
//选择客户代码的时候，带出客户名称
// const handleChangeCustomer = (e) => {
//   let item = customerPageList.value.find((item) => {
//     return item.id == e;
//   });
//   formData.customerName = item.customerName;
// };

// 客户
const postCustomerPageList = async () => {
  let res = await postCustomerPage();
  customerPageList.value = res.data.map((item) => {
    return {
      label: item.customerNo + "\u0020/\u0020" + item.customerName,
      value: item.id,
      customerNo: item.customerNo,
      id: item.id,
    };
  });
};
// 产品名称productList
const postproductList = async () => {
  let res = await productList({}, { ignoreCancel: true });

  productListData.value = res.data.map((item) => {
    item.productName = item.productName + " ( " + item.productNo + " ) ";
    return item;
  });
};
// 关联-销售合同
const postNoContractReview = async () => {
  let param = {
    statusList: [0, 1, 4],
  };

  let res = await postNoContractReviewList(param);
  postNoContractReviewListOptions.value = res.data;
};

// 编辑详情
// contractReviewId
const getDetail = async (val) => {
  let res = await getContractReviewDetail({ id: val });
  formData.status = res.data.status;
  formData.contractNo = res.data.contractNo;
  formData.orderNo = res.data.orderNo;
  formData.contractClassify = res.data.contractClassify;
  formData.relationContractId = res.data.relationContractId;
  formData.invoiceType = res.data.invoiceType;
  formData.contractName = res.data.contractName;
  formData.contractType = res.data.contractType;
  formData.customerId = res.data.customerId;
  formData.customerName = res.data.customerName;
  formData.productMarketClassification = res.data.productMarketClassification;
  formData.reviewTime = res.data.reviewTime;
  formData.deliveryTime = res.data.deliveryTime;
  formData.belongUserId = res?.data?.bizBelongUserId
    ? Number(res.data.bizBelongUserId)
    : void 0;
  if (res.data.file && res.data.file.length > 0) {
    formData.file = res.data.file;
  } else {
    formData.file = [];
  }

  formData.relationProductList = res.data.contractMaterialRelationList.map(
    (item) => {
      item.quantity = item.totalQuantity;
      return item;
    }
  );
  formData.remark = res.data.remark;
  formData.currencyType = res.data.currencyType;
  formData.useCountry = res.data.useCountry;
  formData.foreignCountry = res.data.foreignCountry;
};

// 产品明细添加
const hasMaterialList = ref(false);
const allMaterialList = ref([]);
const filterOptionM = (input, option) => {
  return (
    option.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0 ||
    option.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0
  );
};
const getAllMaterialList = async (keyword) => {
  let result = await AllPage({
    ignoreStop: true,
    isMarket: 1,
    keyword: keyword,
  });
  let { data } = result;
  allMaterialList.value = data.map((item) => {
    // label: `${item.materialNo}\u0020/\u0020${item.materialName}${
    // props.showSpecification ? "\u0020/\u0020" + item.specification : ""
    // }`,
    return {
      label: item.fileNumber
        ? `${item.materialNo}\u0020/\u0020${item.materialName}\u0020/\u0020${item.specification}\u0020/\u0020${item.fileNumber}`
        : `${item.materialNo}\u0020/\u0020${item.materialName}\u0020/\u0020${item.specification}`,
      value: item.id,
      ...item,
    };
  });
  hasMaterialList.value = true;
};
const handleAdd = async () => {
  if (hasMaterialList.value) {
    //
  } else {
    await getAllMaterialList();
  }
  const newData = {
    id: Date.now(),
  };
  formData.relationProductList.push({
    unitPrice: 0,
    quantity: 0,
  });
};
// 产品删除
const onDelete = async (val, ind) => {
  formData.relationProductList.map((item, index) => {
    if (index == ind) {
      if (item.materialId == val.materialId) {
        formData.relationProductList.splice(index, 1);
        onProductData(item.materialId, val, item);
      }
    }
    return item;
  });
  formData.relationProductList = formData.relationProductList;
};

function hasDuplicates(array) {
  return new Set(array).size !== array.length;
}
// 确认新增
const formSubmit = async () => {
  formRef.value.validate().then(async () => {
    try {
      const hasEmptyName = formData.relationProductList.some((obj) => {
        return (
          obj?.materialId === null ||
          obj?.materialId === undefined ||
          obj?.materialId === ""
        );
      });
      if (hasEmptyName) {
        message.warning("请选择产品");
        return;
      }
      const newArr = formData.relationProductList.map(
        (item) => item.materialId
      );
      if (hasDuplicates(newArr)) {
        message.warning("产品不得重复");
        return;
      }
      const isNotEmpty = (value) => {
        return (
          value !== null && value !== undefined && value !== "" && value !== 0
        );
      };
      const isNotEmptyExclude0 = (value) => {
        console.log("[ value ] >", value);
        return value !== null && value !== undefined && value !== "";
      };
      const unitPriceNoEmptyData = formData.relationProductList.every((obj) => {
        return isNotEmptyExclude0(obj?.unitPrice);
      });
      const quantityNoEmptyData = formData.relationProductList.every((obj) => {
        return isNotEmpty(obj?.quantity);
      });
      const taxRateNoEmptyData = formData.relationProductList.every((obj) => {
        return isNotEmptyExclude0(obj?.taxRate);
      });

      if (!unitPriceNoEmptyData) {
        proxy.$message.warning("单价不得为空");
        return;
      }
      if (!quantityNoEmptyData) {
        proxy.$message.warning("数量不得为0");
        return;
      }
      if (!taxRateNoEmptyData) {
        proxy.$message.warning("税率不得为空");
        return;
      }

      if (formData.contractNo && formData.orderNo) {
        if (
          formData.contractNo.includes("Z-") ||
          formData.contractNo.includes("z-") ||
          formData.orderNo.includes("Z-") ||
          formData.orderNo.includes("z-")
        ) {
          proxy.$message.warning("合同/订单号不得以Z-开头");
          return;
        }
      }
      let param = {};
      if (formData.invoiceType == "Prefetch number") {
        delete formData?.reviewTime;
        delete formData?.deliveryTime;
      } else {
        if (!props.id) {
          if (formData.relationProductList.length <= 0) {
            proxy.$message.warning("产品明细不能为空!");
            return;
          }
        }
      }
      param = {
        ...formData,
        autoGenerationCodeFlag: 1,
        relationMaterialList: formData.relationProductList,
        // 暂时不处理，接口报错
        // file: formData.file && formData.file[0],
      };
      if (props.id) {
        param.id = props.id;
        param.bizBelongUserId = formData.belongUserId;
      }
      loading.value = true;
      let res;
      if (props.id) {
        res = await postContractReviewUpdate(param);
      } else {
        res = await postContractReviewAdd(param);
      }
      if (res.code == 200) {
        proxy.$message.success("操作成功");
        onClose();
      }
      loading.value = false;
      emit("finish");
    } catch (error) {
      loading.value = false;
    }
  });
};
const gotoBom = (bomNo) => {
  onClose();

  router.push({
    name: "Bom",
    params: { bomNo: bomNo },
  });
  store.setParamsBomNo(bomNo);
};
function onClose() {
  props.type = void 0;
  props.id = void 0;
  formData.contractClassify = void 0;
  formData.relationContractId = void 0;
  formData.invoiceType = void 0;
  formData.contractName = void 0;
  formData.contractType = void 0;
  formData.customerId = void 0;
  formData.productMarketClassification = void 0;
  formData.reviewTime = void 0;
  formData.deliveryTime = void 0;
  formData.relationProductList = [{}];
  formData.remark = void 0;
  formData.file = [];
  emit("update:visible", false);
  formRef.value.resetFields();
}
// 字典查询
const getDictDataType = async () => {
  let dictType = "market_currency_type";
  let res = await dictDataType(dictType);
  dictTypeData.value = res.data;
};
const changeAutoGenerationCodeFlag = () => {
  formData.orderNo = void 0;
  formData.contractNo = void 0;
};

// 生成合同编号预览
const generateContractNoPreview = () => {
  const now = new Date();
  const year = now.getFullYear().toString().slice(-2); // 年份后两位
  const month = (now.getMonth() + 1).toString(); // 月份

  // 工厂编号规则
  let factoryNo = "--";
  if (store.user.tenantId === 28) {
    factoryNo = "1";
  } else if (store.user.tenantId === 37) {
    factoryNo = "2";
  }

  // 业务类别编号
  let businessNo = "--";
  if (formData.contractType) {
    const businessType = businessTypes.find(
      (item) => item.value === formData.contractType
    );
    if (businessType && businessType.no) {
      businessNo = businessType.no;
    }
  }

  // 产品类别编号
  let productNo = "--";
  if (formData.productMarketClassification) {
    const productType = productTypes.find(
      (item) => item.value === formData.productMarketClassification
    );
    if (productType && productType.no) {
      productNo = productType.no;
    }
  }

  // 合同管理人编号（默认--）
  const managerNo = "--";

  // 序号（默认--）
  const serialNo = "--";

  return `${year}${month}${factoryNo}${businessNo}${productNo}${managerNo}${serialNo}`;
};

// 防抖调用接口生成完整合同编号
const debouncedGenerateContractNumber = debounce(async () => {
  // 检查是否所有必要字段都已选择，包括客户代码
  if (
    formData.contractType &&
    formData.productMarketClassification &&
    formData.belongUserId
  ) {
    try {
      const res = await getContractNumber({
        contractType: formData.contractType,
        productMarketClassification: formData.productMarketClassification,
        belongUserId: formData.belongUserId,
      });
      console.log("[ res,res.data,res.data.data ] >", res, res.data, res.data);
      if (res.code === 200 && res.data) {
        formData.contractNo = res.data;
      }
    } catch (error) {
      console.error("生成合同编号失败:", error);
    }
  }
}, 500);

// 更新合同编号
const updateContractNo = () => {
  if (props.type === "det") return; // 详情页不更新
  // 生成预览编号
  formData.contractNo = generateContractNoPreview();
  // 如果所有必要字段都已选择，包括客户代码，则调用接口生成完整编号
  if (
    formData.contractType &&
    formData.productMarketClassification &&
    formData.belongUserId
  ) {
    debouncedGenerateContractNumber();
  }
};

// 处理业务类别变化
const handleContractTypeChange = (value) => {
  formData.contractType = value;
  updateContractNo();
};

// 处理产品类别变化
const handleProductTypeChange = (value) => {
  formData.productMarketClassification = value;
  updateContractNo();
};

// 处理合同管理人变化
const handleManagerChange = (value) => {
  formData.belongUserId = value;
  // 只有当客户代码存在时才更新合同编号
  updateContractNo();
};

watch(
  () => props.visible,
  async (val) => {
    if (val) {
      await getUserList();
      await postCustomerPageList();
      await postNoContractReview();
      await getDictDataType();
      if (props.id) {
        await getDetail(props.id);
        await getAllMaterialList();
      } else {
        // 新增时初始化合同编号
        updateContractNo();
      }
    }
  }
);
</script>
<style lang="less" scoped></style>
