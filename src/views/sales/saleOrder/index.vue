<template>
  <div class="mb-4 text-right">
    <search :searchData="searchData" @search="refresh">
      <mw-button
        :font="'iconfont icon-xianxing-121'"
        v-permission="'market:order:add'"
        @click="onOpen('', 'add')"
        >新增</mw-button
      >
    </search>
  </div>
  <mw-table
    :scroll="{ x: 'max-content' }"
    :columns="visibleColumns"
    :data-source="data"
    :loading="loading"
    :rowKey="(record) => record.id"
    hasPage
    @change="onTableChange"
    :pageConfig="paginationProps"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key == 'status'">
        <dictionary
          :statusOptions="purchaseReturnsList"
          :value="record.status"
          isBackgroundColor
        />
      </template>

      <template v-if="column.key == 'type'">
        <span v-if="record.type == 'PRODUCTION_ORDER'"
          >生产订单({{ record.contractNo }})</span
        >
        <span v-else-if="record.type == 'OUT_SOURCE_ORDER'"
          >外协订单({{ record.contractNo }})</span
        >
        <dictionary
          v-else
          :statusOptions="orderTypeList"
          :value="String(record.type)"
          :statusExtend="record.statusExtend"
      /></template>
      <template v-if="column.key == 'button'">
        <mw-button @click="onOpenDetail(record.id, 'detail')" class="mr-2"
          >详情</mw-button
        >
        <mw-button
          v-permission="'market:order:exportExcel'"
          @click="exportOrg(record)"
          class="mr-2"
          >导出</mw-button
        >

        <a-popconfirm
          v-if="record.status != 5 && record.status != 3 && record.status != 4"
          detailContractReviewDrawer
          title="确定是否删除"
          ok-text="是"
          cancel-text="否"
          @confirm="onCancelFirm(record)"
        >
          <mw-button danger>取消</mw-button>
        </a-popconfirm>

        <!-- <a-button
          type="primary"
          @click="onGift(record.id)"
          class="ml-2"
          v-permission="'market:order:addGift'"
        >
          赠品
        </a-button> -->
        <!-- 这次版本隐藏 -->
        <!-- <a-button
          type="primary"
          @click="onOpenDet(record, 'edit')"
          class="ml-2"
          v-if="record.status == 9 || record.status == 2 || record.status == 10"
        >
          编辑
        </a-button> -->
      </template>
    </template>
  </mw-table>
  <!-- 新增 -->
  <add-contract-review-drawer
    ref="refAddContractReviewDrawer"
    v-model:visible="visibleAddContractReviewDrawer"
    @finish="getList"
    v-model:id="contractReviewId"
    :contractReviewStatus="contractReviewStatus"
    :openType="openType"
  />

  <!-- <giftDrawer
    v-model:visible="visibleGift"
    v-model:id="contractReviewId"
    @finish="getList"
  ></giftDrawer> -->
  <giftTabs
    v-model:visible="visibleGiftTabs"
    v-model:id="contractReviewId"
    @finish="getList"
  ></giftTabs>
</template>
<script setup>
import Search from "@/components/search/index.vue";
import { ref, onBeforeMount, toRaw, getCurrentInstance, computed } from "vue";
import { usePagenation } from "@/common/setup";
import addContractReviewDrawer from "./addSaleOrderDrawer.vue";
import {
  orderTypeList,
  wholeType,
  orderConfigList,
} from "@/common/constant.js";
import { purchaseReturnsList } from "@/common/constant.js";
import { useCountryList } from "@/common/constant.js";
import { postSaleOrderList } from "@/api/sales/index.js";
import { postCancel } from "@/api/sales/index.js";
import { marketOrderExportExcel } from "@/api/sales/index.js";
import giftTabs from "./giftTabs.vue";
import { exportExecl } from "@/utils/util.js";
import { useUserStore } from "@/stores/user.js";
const store = useUserStore();
const contractReviewStatus = ref();
const visibleGiftTabs = ref(false);
const exportLoading = ref(false);
const columns = ref([
  {
    title: "销售订单名称",
    dataIndex: "orderName",
    key: "orderName",
  },
  {
    title: "使用国家",
    dataIndex: "foreignCountry",
    key: "foreignCountry",
  },
  {
    title: "销售订单状态",
    dataIndex: "status",
    key: "status",
  },
  {
    title: "销售订单编号",
    dataIndex: "orderNo",
    key: "orderNo",
  },
  {
    title: "订单类型",
    dataIndex: "type",
    key: "type",
  },
  {
    title: "产品总数",
    dataIndex: "productQuantity",
    key: "productQuantity",
  },
  {
    title: "产成品数量",
    dataIndex: "finishedQuantity",
    key: "finishedQuantity",
  },
  {
    title: "已发货数量",
    dataIndex: "quantityShipped",
    key: "quantityShipped",
  },
  {
    title: "交货日期",
    dataIndex: "deliveryTime",
    key: "deliveryTime",
  },
  {
    title: "操作",
    dataIndex: "button",
    key: "button",
    fixed: "right",
  },
]);

//使用计算属性
const visibleColumns = computed(() => {
  //判断条件，举例条件（可根据自己条件进行判断）
  if (searchData.value.fields.useCountry.value == 1) {
    return columns.value;
  } else {
    return columns.value.filter(
      (column) => column.dataIndex !== "foreignCountry"
    );
  }
});
const searchData = ref({
  searchButtons: [],
  operationButtons: [],
  fields: {
    status: {
      name: "产品分类",
      type: "a-select",
      options: [
        {
          label: "全部类型",
          value: "",
        },
        ...purchaseReturnsList.filter((item) =>
          [0, 6, 5, 7, 9, 10].includes(item.value)
        ),
      ],
      placeholder: "选择分类",
      width: "120px",
      value: "",
      allowClear: true,
    },
    useCountry: {
      name: "使用国家",
      type: "a-select",
      options: [...wholeType, ...useCountryList],
      placeholder: "选择国家",
      width: "120px",
      value: "",
      allowClear: true,
    },
    orderType: {
      name: "订单类型",
      type: "a-select",
      options: [...wholeType, ...orderTypeList],
      placeholder: "订单类型",
      width: "120px",
      // value: "",
      allowClear: true,
    },
    rangeDate: {
      type: "a-range-picker",
      value: [],
      valueFormat: "YYYY-MM-DD",
      width: "240px",
      placeholder: ["创建开始日期", "创建结束日期"],
      allowClear: true,
    },
    rangeDate1: {
      type: "a-range-picker",
      value: [],
      valueFormat: "YYYY-MM-DD",
      width: "240px",
      placeholder: ["交货开始日期", "交货结束日期"],
      allowClear: true,
    },
    keyword: {
      type: "a-input-search",
      placeholder: "请输入订单编号/订单名称",
      width: "240px",
      allowClear: true,
    },
    packingRequirement: {
      name: "包装要求",
      type: "a-select",
      options: orderConfigList.packingRequirement,
      placeholder: "包装要求",
      width: "200px",
      // value: "",
      allowClear: true,
      mode: "multiple",
      maxTagCount: 1,
      permission: "market:order:furuishisearch",
    },
  },
});

const { proxy } = getCurrentInstance();
const visibleAddContractReviewDrawer = ref(false);
const openType = ref();
const contractReviewId = ref("");
const data = ref([]),
  loading = ref(false);
const refAddContractReviewDrawer = ref(null);
const getList = async () => {
  loading.value = true;
  //分页信息
  let searchParam = {};

  //搜索信息

  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  // 判断查询信息是否存在
  if (store.$state.paramsBomNo) {
    searchData.value.fields.keyword.value = store.$state.paramsBomNo;
    searchParam.keyword = store.$state.paramsBomNo;
  }
  let { rangeDate } = toRaw(searchParam);
  if (rangeDate && rangeDate.length > 0) {
    searchParam.startTime = rangeDate[0] + " 00:00:00";
    searchParam.endTime = rangeDate[1] + " 23:59:59";
  }
  let { rangeDate1 } = toRaw(searchParam);
  if (rangeDate1 && rangeDate1.length > 0) {
    searchParam.startDeliveryTime = rangeDate1[0] + " 00:00:00";
    searchParam.endDeliveryTime = rangeDate1[1] + " 23:59:59";
  }
  //
  let { packingRequirement } = toRaw(searchParam);
  if (packingRequirement) {
    searchParam.marketOrderConfigParam = {
      packingRequirement,
    };
  }
  let result = await postSaleOrderList(
    { ...pageParam.value },
    { ...searchParam, packingRequirement: undefined }
  );
  data.value = result.data;
  store.setParamsBomNo();
  paginationProps.value.total = result.total;
  loading.value = false;
};

onBeforeMount(async () => {
  await getList();
});
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);

const onOpen = (valId, type) => {
  contractReviewId.value = valId;
  visibleAddContractReviewDrawer.value = true;
  openType.value = type;
};
// 编辑的事件暂时隐藏，后期编辑可能会继续使用
// const onOpenDet = (val, type) => {
//   contractReviewId.value = val.id;
//   contractReviewStatus.value = val.status;
//   openType.value = type;
//   visibleAddContractReviewDrawer.value = true;
// };
const onOpenDetail = (valId, type) => {
  contractReviewId.value = valId;
  visibleAddContractReviewDrawer.value = true;
  openType.value = type;
};

async function onCancelFirm(record) {
  let params = {
    orderId: record.id,
  };
  let res = await postCancel(params);
  if (res.code == 200) {
    proxy.$message.success("取消成功");
  }
  getList();
}

// //赠品---暂时注释，后期赠品放开即可使用
// const onGift = (val) => {
//   contractReviewId.value = val;
//   visibleGiftTabs.value = true;
// };
// 导出
const exportOrg = async (record) => {
  exportLoading.value = true;

  let result = await marketOrderExportExcel({ orderId: record.id });
  const fileName = "销售订单.xlsx";
  exportExecl(fileName, result);
  exportLoading.value = false;
};
</script>
<style lang="less" scoped></style>
