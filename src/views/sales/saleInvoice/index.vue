<template>
  <div class="mb-4 text-right">
    <search :searchData="searchData" @search="refresh">
      <mw-button @click="exportOrg" v-permission="'market:order:exportExcel'"
        >导出</mw-button
      >
      <!-- <mw-button :font="'iconfont icon-xianxing-121'" @click="onOpenBill('add')"
        >新增</mw-button
      > -->
      <mw-button
        :font="'iconfont icon-xianxing-121'"
        @click="onOpenBill('add', 1)"
        >新增蓝字发票</mw-button
      >
      <mw-button
        :font="'iconfont icon-xianxing-121'"
        @click="onOpenBill('add', 2)"
        >新增红字发票</mw-button
      >
      <mw-button @click="onOpenBill('firstTime', 1)">期初处理</mw-button>
    </search>
  </div>
  <mw-table
    :scroll="{ x: 'max-content' }"
    :columns="columns"
    :data-source="data"
    :loading="loading"
    :rowKey="(record) => record.id"
    hasPage
    @change="onTableChange"
    :pageConfig="paginationProps"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key == 'status'">
        <dictionary :statusOptions="invoiceStatus" :value="record.status" />
      </template>
      <template v-if="column.key == 'makeInvoiceType'">
        <dictionary
          :statusOptions="billingTypeList"
          :value="record.makeInvoiceType"
        />
      </template>
      <template v-if="column.key == 'issueInvoiceType'">
        {{
          record.issueInvoiceType == 2
            ? "红字发票"
            : record.issueInvoiceType == 1
            ? "蓝字发票"
            : "-"
        }}
      </template>

      <template v-if="column.key == 'orderAmount'">
        {{ record.orderAmount }}
        <dictionary
          :statusOptions="dictTypeData"
          :value="record.currencyType"
        />
      </template>
      <template v-if="column.key == 'invoiceAmount'">
        {{ record.invoiceAmount }}
        <dictionary :statusOptions="dictTypeData" :value="record.currencyType"
      /></template>
      <!-- <template v-if="column.key == 'productMarketClassification'">
        <dictionary
          :statusOptions="productMarketClassificationList"
          :value="record.productMarketClassification"
          isBackgroundColor
        />
      </template> -->

      <!--    v-if="record.isBeginning == 0" -->
      <template v-if="column.key == 'button'">
        <mw-button
          :title="'详情'"
          @click="onOpen('detail', record.id, record.isBeginning)"
        ></mw-button>

        <!-- <a-button type="primary" @click="onOpen('edit',record.id)" class="ml-2">
          编辑
        </a-button> -->
      </template>
    </template>
  </mw-table>
  <!-- 新增 -->
  <add-contract-review-drawer
    ref="refAddContractReviewDrawer"
    v-model:visible="visibleAddContractReviewDrawer"
    @finish="getList"
    v-model:id="contractReviewId"
    :openType="openType"
    :isBeginning="isBeginning"
    :billType="CurrentBillType"
  />
  <addSaleOrderDrawerRed
    ref="refAddContractReviewDrawer"
    v-model:visible="visibleAddContractReviewDrawerRed"
    @finish="getList"
    v-model:id="contractReviewId"
    :openType="openType"
    :isBeginning="isBeginning"
    :billType="CurrentBillType"
  />
</template>
<script setup>
import Search from "@/components/search/index.vue";
import { message } from "ant-design-vue";
import { ref, onBeforeMount, reactive, toRaw, getCurrentInstance } from "vue";
import { usePagenation } from "@/common/setup";
import addContractReviewDrawer from "./addSaleOrderDrawer.vue";
import addSaleOrderDrawerRed from "./addSaleOrderDrawerRed.vue";

import { postInvoicePage, invoiceExportExcel } from "@/api/sales/index.js";
import { exportExecl } from "@/utils/util.js";
import {
  invoiceStatus,
  billingTypeList,
  billTypes,
} from "@/common/constant.js";
import { dictDataType } from "@/api/sales/index.js";
import { productMarketClassificationList } from "@/common/constant.js";

const columns = ref([
  {
    title: "发票单据编号",
    dataIndex: "invoiceNumber",
    key: "invoiceNumber",
  },
  {
    title: "开具",
    dataIndex: "issueInvoiceType",
    key: "issueInvoiceType",
  },
  {
    title: "期初",
    dataIndex: "isBeginning",
    key: "isBeginning",
    customRender: ({ record }) => {
      return record.isBeginning == 1
        ? "是"
        : record.isBeginning == 0
        ? "否"
        : "";
    },
  },
  {
    title: "创建日期",
    dataIndex: "createTime",
    key: "createTime",
  },
  {
    title: "客户简称",
    dataIndex: "customerName",
    key: "customerName",
  },
  {
    title: "发票状态",
    dataIndex: "status",
    key: "status",
  },

  {
    title: "订单合同名称",
    dataIndex: "marketOrder",
    key: "marketOrder",
  },
  {
    title: "销售订单号",
    dataIndex: "marketOrderNo",
    key: "marketOrderNo",
  },

  {
    title: "订单金额",
    dataIndex: "orderAmount",
    key: "orderAmount",
  },
  {
    title: "开票类型",
    dataIndex: "makeInvoiceType",
    key: "makeInvoiceType",
  },

  {
    title: "开票金额",
    dataIndex: "invoiceAmount",
    key: "invoiceAmount",
  },
  {
    title: "开票号",
    dataIndex: "invoiceNo",
    key: "invoiceNo",
  },
  {
    title: "备注",
    dataIndex: "remark",
    key: "remark",
  },
  // {
  //   title: "市场类型",
  //   dataIndex: "productMarketClassification",
  //   key: "productMarketClassification",
  // },
  {
    title: "操作",
    dataIndex: "button",
    key: "button",
    fixed: "right",
  },
]);

const searchData = ref({
  searchButtons: [],
  operationButtons: [],
  fields: {
    rangeDate: {
      type: "a-range-picker",
      value: [],
      valueFormat: "YYYY-MM-DD",
      width: "240px",
      placeholder: ["开始日期", "结束日期"],
      allowClear: true,
    },
    status: {
      name: "发票状态",
      type: "a-select",
      options: invoiceStatus,
      placeholder: "发票状态",
      width: "120px",
      optionFilterProp: "label",
      fieldNames: {
        label: "label",
        value: "value",
      },
      allowClear: true,
    },
    issueInvoiceType: {
      type: "a-select",
      placeholder: "开具发票",
      options: billTypes,
      width: "120px",
      allowClear: true,
    },
    keyword: {
      type: "a-input-search",
      placeholder: "发票单据编号/销售订单/客户编码/客户名称/票据号",
      width: "400px",
      allowClear: true,
    },
  },
});

const visibleAddContractReviewDrawer = ref(false);
const visibleAddContractReviewDrawerRed = ref(false);
const openType = ref();
const contractReviewId = ref("");
const data = ref([]);
const loading = ref(false);
const refAddContractReviewDrawer = ref(null);
const exportLoading = ref(false);
const dictTypeData = ref();
const isBeginning = ref();

const getList = async () => {
  loading.value = true;
  //分页信息
  let searchParam = {};
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  let { rangeDate } = toRaw(searchParam);
  if (rangeDate && rangeDate.length > 0) {
    searchParam.startTime = rangeDate[0] + " 00:00:00";
    searchParam.endTime = rangeDate[1] + " 23:59:59";
  }
  // delete  searchData.value.fields.rangeDate

  let result = await postInvoicePage({ ...pageParam.value }, searchParam);
  // let result = await postInvoicePage(pageParam.value, param);
  data.value = result.data;
  paginationProps.value.total = result.total;
  loading.value = false;
};

const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);

const onOpen = (type, valId, isBeginningVal) => {
  openType.value = type;
  contractReviewId.value = valId;
  visibleAddContractReviewDrawer.value = true;
  isBeginning.value = isBeginningVal;
};
const CurrentBillType = ref();
const onOpenBill = (type, billType) => {
  openType.value = type;
  contractReviewId.value = "";
  CurrentBillType.value = billType;
  if (billType == 1) {
    visibleAddContractReviewDrawer.value = true;
  } else {
    visibleAddContractReviewDrawerRed.value = true;
  }
};

// const onOpenDetail = (type, valId) => {
//   contractReviewId.value = valId;
//   visibleDetail.value = true;
// };
const getDictDataType = async () => {
  let dictType = "market_currency_type";
  let res = await dictDataType(dictType);
  dictTypeData.value = res.data.map((item) => {
    return { label: item.dictLabel, value: item.dictValue };
  });
};
async function onConfirm(record) {
  getList();
}
const onCancel = (record) => {
  // message.error('Click on No');
};
// 导出
const exportOrg = async () => {
  exportLoading.value = true;
  let searchParam = {};
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  let { rangeDate } = toRaw(searchParam);
  if (rangeDate && rangeDate.length > 0) {
    searchParam.startTime = rangeDate[0] + " 00:00:00";
    searchParam.endTime = rangeDate[1] + " 23:59:59";
  }
  let result = await invoiceExportExcel({
    ...searchParam,
  });
  const fileName = "销售发票.xlsx";
  exportExecl(fileName, result);
  exportLoading.value = false;
};
onBeforeMount(async () => {
  await getList();
  await getDictDataType();
});
</script>
<style lang="less" scoped>
:deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
  background: #fff;
}
:deep(.ant-table-tbody > tr > td.ant-table-cell-row-hover) {
  background: #fff;
}
</style>
