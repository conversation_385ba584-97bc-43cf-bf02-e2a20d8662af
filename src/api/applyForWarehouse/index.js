import { Get, Post, Put, Delete } from "@/common/request";

// 领用列表
export function listApplyForWarehouse(bodyParam, urlParam) {
  return Post({
    url: "/wms/warehouse/material/return/page",
    bodyParam,
    urlParam
  });
}

export function detailApplyForWarehouse(urlParam) {
  return Get({
    url: "/wms/warehouse/material/return/detail?materialReturnId=" + urlParam,
  });
}

// 领用新增
export function saveApplyForWarehouse(bodyParam) {
  return Post({
    url: "/wms/warehouse/material/return/saveOrUpdate",
    bodyParam,
  });
}
// 选择物料列表
export function listMaterial(bodyParam) {
  return Post({
    url: "/wms/material/page",
    bodyParam,
  });
}
// 字典
export function dictionary(param) {
  return Get({
    url: "/system/dict/data/type/" + param,
  })
}

