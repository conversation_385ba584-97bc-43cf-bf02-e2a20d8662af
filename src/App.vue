<template>
  <a-config-provider
    :locale="zhCN"
    :transformCellText="({ text, column, record, index }) => text || '-'"
    :form="formConfig"
  >
    <a-layout style="min-height: 100vh">
      <router-view />
      <div
        class="w-2 h-2 fixed right-0 bottom-0 bg-ff z-1000"
        v-if="isDD"
      ></div>
    </a-layout>
    <a-modal
      v-model:open="dialogVisible"
      title="提示："
      @cancel="handleOk"
      @ok="handleOk"
      :closable="false"
      :cancelButtonProps="{ style: { display: 'none' } }"
    >
      <p>当前登录用户已更新</p>
      <p>请关闭当前页面重新进入</p>
    </a-modal>
  </a-config-provider>
</template>
<script setup>
import zhCN from "ant-design-vue/es/locale/zh_CN.js";
//ant-design-vue dayjsv3版本 moment是vue2版本

import "dayjs/locale/zh-cn";
import dayjs from "dayjs";
import { env, runtime } from "dingtalk-jsapi";
import {
  reactive,
  toRefs,
  ref,
  computed,
  watch,
  onMounted,
  onBeforeMount,
  getCurrentInstance,
  nextTick,
  onBeforeUnmount,
} from "vue";
import { useUserStore } from "@/stores/user.js";
import { useRoute, useRouter } from "vue-router";
import { logInAppIdOrAgentId } from "@/api/login.js";
import { setToken, getToken, removeToken, removeTenant } from "@/utils/auth.js";
dayjs.locale("zh-cn");
const formConfig = ref({
  colon: false,
});
const isInDingtalk = computed(() => {
  return env.platform != "notInDingTalk";
});
const router = useRouter();
const route = useRoute();
const userStore = useUserStore();
const isDingTalk = async () => {
  const token = getToken();
  if (token) {
    let res = await logInAppIdOrAgentId();
    if (res.data) {
      await userStore.LogOut();
      nextTick(() => {
        router.push("/login");
      });
    }
  }
};
const isDD = ref(false);
// 接收端（App.vue）
const channel = new BroadcastChannel("auth-channel");
const dialogVisible = ref(false);
const initBroadcastChannel = () => {
  channel.value = new BroadcastChannel("auth-channel");
  channel.value.onmessage = async (e) => {
    if (
      e.data.type === "force-refresh" &&
      e.data.tenant_id != userStore.$state.user.tenantId
    ) {
      if (route.path === "/login") {
        // 如果在登录页，不需要处理
        return;
      }
      dialogVisible.value = true;
    }
  };
};
const handleOk = async () => {
  // 刷新页面
  // window.location.reload();
  window.close();
};
onMounted(() => {
  if (isInDingtalk.value) {
    initBroadcastChannel();
  }
});
onBeforeMount(async () => {
  if (isInDingtalk.value) {
    isDD.value = true;
    await isDingTalk();
  }
});
onBeforeUnmount(() => {
  if (channel.value) {
    channel.value.close();
  }
});
</script>
